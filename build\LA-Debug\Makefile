#############################################################################
# Makefile for building: Loong_disp
# Generated by qmake (3.1) (Qt 5.15.12)
# Project:  ../../Loong_disp.pro
# Template: app
# Command: /home/<USER>/arm-qt-ssl/bin/qmake -o Makefile ../../Loong_disp.pro -spec linux-loongarch64-g++ CONFIG+=debug CONFIG+=qml_debug
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = /opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/bin/loongarch64-linux-gnu-gcc
CXX           = /opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/bin/loongarch64-linux-gnu-g++
DEFINES       = -DQT_QML_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_SERIALPORT_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -g -Wall -Wextra -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -g -std=gnu++1z -Wall -Wextra -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I../../../Lonngxin -I. -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I. -I/home/<USER>/openssl/openssl-1.1.1f/include -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++
QMAKE         = /home/<USER>/arm-qt-ssl/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /home/<USER>/arm-qt-ssl/bin/qmake -install qinstall
QINSTALL_PROGRAM = /home/<USER>/arm-qt-ssl/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = Loong_disp1.0.0
DISTDIR = /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/.tmp/Loong_disp1.0.0
LINK          = /opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/bin/loongarch64-linux-gnu-g++
LFLAGS        = -Wl,-rpath,/home/<USER>/arm-qt-ssl/lib
LIBS          = $(SUBLIBS) -L/home/<USER>/openssl/openssl-1.1.1f/lib /home/<USER>/arm-qt-ssl/lib/libQt5Charts.so /home/<USER>/arm-qt-ssl/lib/libQt5Widgets.so /home/<USER>/arm-qt-ssl/lib/libQt5Gui.so /home/<USER>/arm-qt-ssl/lib/libQt5Network.so /home/<USER>/arm-qt-ssl/lib/libQt5SerialPort.so /home/<USER>/arm-qt-ssl/lib/libQt5Core.so -lpthread  -lssl -lcrypto 
AR            = /opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/bin/loongarch64-linux-gnu-ar cqs
RANLIB        = 
SED           = sed
STRIP         = /opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/bin/loongarch64-linux-gnu-strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ../../main.cpp \
		../../node1.cpp \
		../../node2.cpp \
		../../node3.cpp \
		../../setting.cpp \
		../../taskmqtt.cpp \
		../../taskserialport.cpp \
		../../taskwarning.cpp \
		../../widget.cpp \
		../../mqtt/qmqtt_client.cpp \
		../../mqtt/qmqtt_client_p.cpp \
		../../mqtt/qmqtt_frame.cpp \
		../../mqtt/qmqtt_message.cpp \
		../../mqtt/qmqtt_network.cpp \
		../../mqtt/qmqtt_router.cpp \
		../../mqtt/qmqtt_routesubscription.cpp \
		../../mqtt/qmqtt_socket.cpp \
		../../mqtt/qmqtt_ssl_socket.cpp \
		../../mqtt/qmqtt_timer.cpp \
		../../mqtt/qmqtt_websocket.cpp \
		../../mqtt/qmqtt_websocketiodevice.cpp qrc_pic.cpp \
		moc_node1.cpp \
		moc_node2.cpp \
		moc_node3.cpp \
		moc_setting.cpp \
		moc_taskmqtt.cpp \
		moc_taskserialport.cpp \
		moc_taskwarning.cpp \
		moc_widget.cpp \
		moc_qmqtt_client.cpp \
		moc_qmqtt_network_p.cpp \
		moc_qmqtt_networkinterface.cpp \
		moc_qmqtt_router.cpp \
		moc_qmqtt_routesubscription.cpp \
		moc_qmqtt_socket_p.cpp \
		moc_qmqtt_socketinterface.cpp \
		moc_qmqtt_ssl_socket_p.cpp \
		moc_qmqtt_timer_p.cpp \
		moc_qmqtt_timerinterface.cpp \
		moc_qmqtt_websocket_p.cpp \
		moc_qmqtt_websocketiodevice_p.cpp
OBJECTS       = main.o \
		node1.o \
		node2.o \
		node3.o \
		setting.o \
		taskmqtt.o \
		taskserialport.o \
		taskwarning.o \
		widget.o \
		qmqtt_client.o \
		qmqtt_client_p.o \
		qmqtt_frame.o \
		qmqtt_message.o \
		qmqtt_network.o \
		qmqtt_router.o \
		qmqtt_routesubscription.o \
		qmqtt_socket.o \
		qmqtt_ssl_socket.o \
		qmqtt_timer.o \
		qmqtt_websocket.o \
		qmqtt_websocketiodevice.o \
		qrc_pic.o \
		moc_node1.o \
		moc_node2.o \
		moc_node3.o \
		moc_setting.o \
		moc_taskmqtt.o \
		moc_taskserialport.o \
		moc_taskwarning.o \
		moc_widget.o \
		moc_qmqtt_client.o \
		moc_qmqtt_network_p.o \
		moc_qmqtt_networkinterface.o \
		moc_qmqtt_router.o \
		moc_qmqtt_routesubscription.o \
		moc_qmqtt_socket_p.o \
		moc_qmqtt_socketinterface.o \
		moc_qmqtt_ssl_socket_p.o \
		moc_qmqtt_timer_p.o \
		moc_qmqtt_timerinterface.o \
		moc_qmqtt_websocket_p.o \
		moc_qmqtt_websocketiodevice_p.o
DIST          = ../../mqtt/qmqtt.pri \
		../../mqtt/qmqtt.qbs \
		/home/<USER>/arm-qt-ssl/mkspecs/features/spec_pre.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/unix.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/linux.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/sanitize.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/gcc-base.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/gcc-base-unix.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/g++-base.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/g++-unix.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/qconfig.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_freetype.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_libjpeg.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_libpng.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_charts.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_charts_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_concurrent.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_concurrent_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_core.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_core_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_dbus.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_dbus_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_edid_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_fb_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_gui.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_gui_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_input_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimedia.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimedia_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_network.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_network_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_printsupport.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_printsupport_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialbus.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialbus_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialport.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialport_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_service_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_sql.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_sql_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_svg.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_svg_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_testlib.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_testlib_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_theme_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_webchannel.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_webchannel_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_websockets.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_websockets_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_widgets.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_widgets_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_xml.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_xml_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_zlib_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qt_functions.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qt_config.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++/qmake.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/spec_post.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/exclusive_builds.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/toolchain.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/default_pre.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/resolve_config.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/default_post.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qml_debug.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/warn_on.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qt.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/resources_functions.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/resources.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/moc.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/uic.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/unix/thread.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qmake_use.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/file_copies.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/testcase_targets.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/exceptions.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/yacc.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/lex.prf \
		../../../../Loong_disp.pro ../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskmqtt.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_client_p.h \
		../../mqtt/qmqtt_frame.h \
		../../mqtt/qmqtt_global.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_message_p.h \
		../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_routedmessage.h \
		../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_routesubscription.h \
		../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_websocket_p.h \
		../../mqtt/qmqtt_websocketiodevice_p.h ../../main.cpp \
		../../node1.cpp \
		../../node2.cpp \
		../../node3.cpp \
		../../setting.cpp \
		../../taskmqtt.cpp \
		../../taskserialport.cpp \
		../../taskwarning.cpp \
		../../widget.cpp \
		../../mqtt/qmqtt_client.cpp \
		../../mqtt/qmqtt_client_p.cpp \
		../../mqtt/qmqtt_frame.cpp \
		../../mqtt/qmqtt_message.cpp \
		../../mqtt/qmqtt_network.cpp \
		../../mqtt/qmqtt_router.cpp \
		../../mqtt/qmqtt_routesubscription.cpp \
		../../mqtt/qmqtt_socket.cpp \
		../../mqtt/qmqtt_ssl_socket.cpp \
		../../mqtt/qmqtt_timer.cpp \
		../../mqtt/qmqtt_websocket.cpp \
		../../mqtt/qmqtt_websocketiodevice.cpp
QMAKE_TARGET  = Loong_disp
DESTDIR       = 
TARGET        = Loong_disp


first: all
####### Build rules

Loong_disp: ui_node1.h ui_node2.h ui_node3.h ui_setting.h ui_widget.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../../Loong_disp.pro /home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++/qmake.conf /home/<USER>/arm-qt-ssl/mkspecs/features/spec_pre.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/unix.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/linux.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/sanitize.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/gcc-base.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/gcc-base-unix.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/g++-base.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/common/g++-unix.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/qconfig.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_freetype.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_libjpeg.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_libpng.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_charts.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_charts_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_concurrent.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_concurrent_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_core.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_core_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_dbus.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_dbus_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_edid_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_fb_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_gui.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_gui_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_input_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimedia.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimedia_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_network.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_network_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_printsupport.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_printsupport_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialbus.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialbus_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialport.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialport_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_service_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_sql.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_sql_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_svg.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_svg_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_testlib.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_testlib_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_theme_support_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_webchannel.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_webchannel_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_websockets.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_websockets_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_widgets.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_widgets_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_xml.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_xml_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_zlib_private.pri \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qt_functions.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qt_config.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++/qmake.conf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/spec_post.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/exclusive_builds.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/toolchain.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/default_pre.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/resolve_config.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/default_post.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qml_debug.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/warn_on.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qt.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/resources_functions.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/resources.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/moc.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/uic.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/unix/thread.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/qmake_use.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/file_copies.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/testcase_targets.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/exceptions.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/yacc.prf \
		/home/<USER>/arm-qt-ssl/mkspecs/features/lex.prf \
		../../Loong_disp.pro \
		../../pic/pic.qrc
	$(QMAKE) -o Makefile ../../Loong_disp.pro -spec linux-loongarch64-g++ CONFIG+=debug CONFIG+=qml_debug
/home/<USER>/arm-qt-ssl/mkspecs/features/spec_pre.prf:
/home/<USER>/arm-qt-ssl/mkspecs/common/unix.conf:
/home/<USER>/arm-qt-ssl/mkspecs/common/linux.conf:
/home/<USER>/arm-qt-ssl/mkspecs/common/sanitize.conf:
/home/<USER>/arm-qt-ssl/mkspecs/common/gcc-base.conf:
/home/<USER>/arm-qt-ssl/mkspecs/common/gcc-base-unix.conf:
/home/<USER>/arm-qt-ssl/mkspecs/common/g++-base.conf:
/home/<USER>/arm-qt-ssl/mkspecs/common/g++-unix.conf:
/home/<USER>/arm-qt-ssl/mkspecs/qconfig.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_freetype.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_libjpeg.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_ext_libpng.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_bootstrap_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_charts.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_charts_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_concurrent.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_concurrent_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_core.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_core_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_dbus.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_dbus_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_edid_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_fb_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_gui.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_gui_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_input_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimedia.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimedia_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimediawidgets.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_network.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_network_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_printsupport.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_printsupport_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialbus.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialbus_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialport.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_serialport_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_service_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_sql.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_sql_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_svg.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_svg_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_testlib.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_testlib_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_theme_support_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_webchannel.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_webchannel_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_websockets.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_websockets_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_widgets.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_widgets_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_xml.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_xml_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/modules/qt_lib_zlib_private.pri:
/home/<USER>/arm-qt-ssl/mkspecs/features/qt_functions.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/qt_config.prf:
/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++/qmake.conf:
/home/<USER>/arm-qt-ssl/mkspecs/features/spec_post.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/exclusive_builds.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/toolchain.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/default_pre.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/resolve_config.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/default_post.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/qml_debug.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/warn_on.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/qt.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/resources_functions.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/resources.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/moc.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/uic.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/unix/thread.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/qmake_use.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/file_copies.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/testcase_targets.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/exceptions.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/yacc.prf:
/home/<USER>/arm-qt-ssl/mkspecs/features/lex.prf:
../../Loong_disp.pro:
../../pic/pic.qrc:
qmake: FORCE
	@$(QMAKE) -o Makefile ../../Loong_disp.pro -spec linux-loongarch64-g++ CONFIG+=debug CONFIG+=qml_debug

qmake_all: FORCE


all: Makefile Loong_disp

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../../pic/pic.qrc $(DISTDIR)/
	$(COPY_FILE) --parents /home/<USER>/arm-qt-ssl/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../../node1.h ../../node2.h ../../node3.h ../../setting.h ../../taskmqtt.h ../../taskserialport.h ../../taskwarning.h ../../widget.h ../../mqtt/qmqtt.h ../../mqtt/qmqtt_client.h ../../mqtt/qmqtt_client_p.h ../../mqtt/qmqtt_frame.h ../../mqtt/qmqtt_global.h ../../mqtt/qmqtt_message.h ../../mqtt/qmqtt_message_p.h ../../mqtt/qmqtt_network_p.h ../../mqtt/qmqtt_networkinterface.h ../../mqtt/qmqtt_routedmessage.h ../../mqtt/qmqtt_router.h ../../mqtt/qmqtt_routesubscription.h ../../mqtt/qmqtt_socket_p.h ../../mqtt/qmqtt_socketinterface.h ../../mqtt/qmqtt_ssl_socket_p.h ../../mqtt/qmqtt_timer_p.h ../../mqtt/qmqtt_timerinterface.h ../../mqtt/qmqtt_websocket_p.h ../../mqtt/qmqtt_websocketiodevice_p.h $(DISTDIR)/
	$(COPY_FILE) --parents ../../main.cpp ../../node1.cpp ../../node2.cpp ../../node3.cpp ../../setting.cpp ../../taskmqtt.cpp ../../taskserialport.cpp ../../taskwarning.cpp ../../widget.cpp ../../mqtt/qmqtt_client.cpp ../../mqtt/qmqtt_client_p.cpp ../../mqtt/qmqtt_frame.cpp ../../mqtt/qmqtt_message.cpp ../../mqtt/qmqtt_network.cpp ../../mqtt/qmqtt_router.cpp ../../mqtt/qmqtt_routesubscription.cpp ../../mqtt/qmqtt_socket.cpp ../../mqtt/qmqtt_ssl_socket.cpp ../../mqtt/qmqtt_timer.cpp ../../mqtt/qmqtt_websocket.cpp ../../mqtt/qmqtt_websocketiodevice.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../../node1.ui ../../node2.ui ../../node3.ui ../../setting.ui ../../widget.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: qrc_pic.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_pic.cpp
qrc_pic.cpp: ../../pic/pic.qrc \
		/home/<USER>/arm-qt-ssl/bin/rcc \
		../../pic/node3.png \
		../../pic/smog1_img.png \
		../../pic/humi1_image.png \
		../../pic/node2.png \
		../../pic/temp1_img.png \
		../../pic/back.png \
		../../pic/node_pic.png \
		../../pic/pic.qrc \
		../../pic/light1_img.png \
		../../pic/set.png
	/home/<USER>/arm-qt-ssl/bin/rcc -name pic ../../pic/pic.qrc -o qrc_pic.cpp

compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /home/<USER>/arm-qt-ssl/mkspecs/features/data/dummy.cpp
	/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/bin/loongarch64-linux-gnu-g++ -pipe -g -std=gnu++1z -Wall -Wextra -dM -E -o moc_predefs.h /home/<USER>/arm-qt-ssl/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_node1.cpp moc_node2.cpp moc_node3.cpp moc_setting.cpp moc_taskmqtt.cpp moc_taskserialport.cpp moc_taskwarning.cpp moc_widget.cpp moc_qmqtt_client.cpp moc_qmqtt_network_p.cpp moc_qmqtt_networkinterface.cpp moc_qmqtt_router.cpp moc_qmqtt_routesubscription.cpp moc_qmqtt_socket_p.cpp moc_qmqtt_socketinterface.cpp moc_qmqtt_ssl_socket_p.cpp moc_qmqtt_timer_p.cpp moc_qmqtt_timerinterface.cpp moc_qmqtt_websocket_p.cpp moc_qmqtt_websocketiodevice_p.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_node1.cpp moc_node2.cpp moc_node3.cpp moc_setting.cpp moc_taskmqtt.cpp moc_taskserialport.cpp moc_taskwarning.cpp moc_widget.cpp moc_qmqtt_client.cpp moc_qmqtt_network_p.cpp moc_qmqtt_networkinterface.cpp moc_qmqtt_router.cpp moc_qmqtt_routesubscription.cpp moc_qmqtt_socket_p.cpp moc_qmqtt_socketinterface.cpp moc_qmqtt_ssl_socket_p.cpp moc_qmqtt_timer_p.cpp moc_qmqtt_timerinterface.cpp moc_qmqtt_websocket_p.cpp moc_qmqtt_websocketiodevice_p.cpp
moc_node1.cpp: ../../node1.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../node1.h -o moc_node1.cpp

moc_node2.cpp: ../../node2.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../node2.h -o moc_node2.cpp

moc_node3.cpp: ../../node3.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../node3.h -o moc_node3.cpp

moc_setting.cpp: ../../setting.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../taskserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../setting.h -o moc_setting.cpp

moc_taskmqtt.cpp: ../../taskmqtt.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../taskmqtt.h -o moc_taskmqtt.cpp

moc_taskserialport.cpp: ../../taskserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../taskserialport.h -o moc_taskserialport.cpp

moc_taskwarning.cpp: ../../taskwarning.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../taskwarning.h -o moc_taskwarning.cpp

moc_widget.cpp: ../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../widget.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../widget.h -o moc_widget.cpp

moc_qmqtt_client.cpp: ../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_client.h -o moc_qmqtt_client.cpp

moc_qmqtt_network_p.cpp: ../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_network_p.h -o moc_qmqtt_network_p.cpp

moc_qmqtt_networkinterface.cpp: ../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_networkinterface.h -o moc_qmqtt_networkinterface.cpp

moc_qmqtt_router.cpp: ../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_router.h -o moc_qmqtt_router.cpp

moc_qmqtt_routesubscription.cpp: ../../mqtt/qmqtt_routesubscription.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QRegularExpression \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QStringList \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_routesubscription.h -o moc_qmqtt_routesubscription.cpp

moc_qmqtt_socket_p.cpp: ../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_socket_p.h -o moc_qmqtt_socket_p.cpp

moc_qmqtt_socketinterface.cpp: ../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_socketinterface.h -o moc_qmqtt_socketinterface.cpp

moc_qmqtt_ssl_socket_p.cpp: ../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_ssl_socket_p.h -o moc_qmqtt_ssl_socket_p.cpp

moc_qmqtt_timer_p.cpp: ../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_timer_p.h -o moc_qmqtt_timer_p.cpp

moc_qmqtt_timerinterface.cpp: ../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_timerinterface.h -o moc_qmqtt_timerinterface.cpp

moc_qmqtt_websocket_p.cpp: ../../mqtt/qmqtt_websocket_p.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_websocket_p.h -o moc_qmqtt_websocket_p.cpp

moc_qmqtt_websocketiodevice_p.cpp: ../../mqtt/qmqtt_websocketiodevice_p.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		moc_predefs.h \
		/home/<USER>/arm-qt-ssl/bin/moc
	/home/<USER>/arm-qt-ssl/bin/moc $(DEFINES) --include /media/hgq/9E3A-F578/Lonngxin/build/LA-Debug/moc_predefs.h -I/home/<USER>/arm-qt-ssl/mkspecs/linux-loongarch64-g++ -I/media/hgq/9E3A-F578/Lonngxin -I/home/<USER>/arm-qt-ssl/include -I/home/<USER>/arm-qt-ssl/include/QtCharts -I/home/<USER>/arm-qt-ssl/include/QtWidgets -I/home/<USER>/arm-qt-ssl/include/QtGui -I/home/<USER>/arm-qt-ssl/include/QtNetwork -I/home/<USER>/arm-qt-ssl/include/QtSerialPort -I/home/<USER>/arm-qt-ssl/include/QtCore -I. -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/lib/gcc/loongarch64-linux-gnu/8.3.0/include-fixed -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++ -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include/c++/backward -I/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/loongarch64-linux-gnu/sysroot/usr/include ../../mqtt/qmqtt_websocketiodevice_p.h -o moc_qmqtt_websocketiodevice_p.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_node1.h ui_node2.h ui_node3.h ui_setting.h ui_widget.h
compiler_uic_clean:
	-$(DEL_FILE) ui_node1.h ui_node2.h ui_node3.h ui_setting.h ui_widget.h
ui_node1.h: ../../node1.ui \
		/home/<USER>/arm-qt-ssl/bin/uic \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h
	/home/<USER>/arm-qt-ssl/bin/uic ../../node1.ui -o ui_node1.h

ui_node2.h: ../../node2.ui \
		/home/<USER>/arm-qt-ssl/bin/uic \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h
	/home/<USER>/arm-qt-ssl/bin/uic ../../node2.ui -o ui_node2.h

ui_node3.h: ../../node3.ui \
		/home/<USER>/arm-qt-ssl/bin/uic \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h
	/home/<USER>/arm-qt-ssl/bin/uic ../../node3.ui -o ui_node3.h

ui_setting.h: ../../setting.ui \
		/home/<USER>/arm-qt-ssl/bin/uic
	/home/<USER>/arm-qt-ssl/bin/uic ../../setting.ui -o ui_setting.h

ui_widget.h: ../../widget.ui \
		/home/<USER>/arm-qt-ssl/bin/uic
	/home/<USER>/arm-qt-ssl/bin/uic ../../widget.ui -o ui_widget.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.o: ../../main.cpp ../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QApplication \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qapplication.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreapplication.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qeventloop.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qguiapplication.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ../../main.cpp

node1.o: ../../node1.cpp ../../node1.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_node1.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o node1.o ../../node1.cpp

node2.o: ../../node2.cpp ../../node2.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_node2.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o node2.o ../../node2.cpp

node3.o: ../../node3.cpp ../../node3.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_node3.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o node3.o ../../node3.cpp

setting.o: ../../setting.cpp ../../setting.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		../../taskserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_setting.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o setting.o ../../setting.cpp

taskmqtt.o: ../../taskmqtt.cpp ../../taskmqtt.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o taskmqtt.o ../../taskmqtt.cpp

taskserialport.o: ../../taskserialport.cpp ../../taskserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDebug \
		/home/<USER>/arm-qt-ssl/include/QtCore/QElapsedTimer
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o taskserialport.o ../../taskserialport.cpp

taskwarning.o: ../../taskwarning.cpp ../../taskwarning.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskmqtt.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o taskwarning.o ../../taskwarning.cpp

widget.o: ../../widget.cpp ../../widget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qwidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtguiglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtgui-config.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmargins.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpaintdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrect.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsize.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpalette.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcolor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgb.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qrgba64.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qbrush.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qmatrix.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpolygon.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qregion.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qline.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtransform.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qimage.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixelformat.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpixmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfont.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontmetrics.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qfontinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qcursor.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qkeysequence.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qvector2d.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtouchdevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonarray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborvalue.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcborcommon.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkAccessManager \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkaccessmanager.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVector \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonDocument \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsondocument.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkReply \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkreply.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qjsonobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QJsonParseError \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChart \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchart.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QAbstractAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qabstractaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QPen \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpen.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QFont \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QLegend \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlegend.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainterpath.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/QBrush \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMargins \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QSplineSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qsplineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qlineseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QXYSeries \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qxyseries.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointF \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QChartView \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qchartview.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QGraphicsView \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qpainter.h \
		/home/<USER>/arm-qt-ssl/include/QtGui/qtextoption.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qframe.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QDateTimeAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCharts/QValueAxis \
		/home/<USER>/arm-qt-ssl/include/QtCharts/qvalueaxis.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTime \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QThread \
		/home/<USER>/arm-qt-ssl/include/QtCore/qthread.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qelapsedtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QMessageBox \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qmessagebox.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qdialog.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/QSerialPort \
		/home/<USER>/arm-qt-ssl/include/QtSerialPort/qserialport.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDir \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdir.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfileinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/QFileDialog \
		/home/<USER>/arm-qt-ssl/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_widget.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o widget.o ../../widget.cpp

qmqtt_client.o: ../../mqtt/qmqtt_client.cpp ../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		../../mqtt/qmqtt_client_p.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QHash \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_client.o ../../mqtt/qmqtt_client.cpp

qmqtt_client_p.o: ../../mqtt/qmqtt_client_p.cpp ../../mqtt/qmqtt_client_p.h \
		../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtCore/QHash \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_frame.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		../../mqtt/qmqtt_message.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QLoggingCategory \
		/home/<USER>/arm-qt-ssl/include/QtCore/qloggingcategory.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUuid \
		/home/<USER>/arm-qt-ssl/include/QtCore/quuid.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFile \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfile.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qfiledevice.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslKey \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslkey.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_client_p.o ../../mqtt/qmqtt_client_p.cpp

qmqtt_frame.o: ../../mqtt/qmqtt_frame.cpp ../../mqtt/qmqtt_frame.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QLoggingCategory \
		/home/<USER>/arm-qt-ssl/include/QtCore/qloggingcategory.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDataStream \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_frame.o ../../mqtt/qmqtt_frame.cpp

qmqtt_message.o: ../../mqtt/qmqtt_message.cpp ../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		../../mqtt/qmqtt_message_p.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedData
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_message.o ../../mqtt/qmqtt_message.cpp

qmqtt_network.o: ../../mqtt/qmqtt_network.cpp ../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h \
		../../mqtt/qmqtt_websocket_p.h \
		../../mqtt/qmqtt_frame.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QDataStream \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatastream.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_network.o ../../mqtt/qmqtt_network.cpp

qmqtt_router.o: ../../mqtt/qmqtt_router.cpp ../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		../../mqtt/qmqtt_routesubscription.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QRegularExpression \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QStringList \
		/home/<USER>/arm-qt-ssl/include/QtCore/QLoggingCategory \
		/home/<USER>/arm-qt-ssl/include/QtCore/qloggingcategory.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_router.o ../../mqtt/qmqtt_router.cpp

qmqtt_routesubscription.o: ../../mqtt/qmqtt_routesubscription.cpp ../../mqtt/qmqtt_routesubscription.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QRegularExpression \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregularexpression.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QStringList \
		../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		../../mqtt/qmqtt_routedmessage.h \
		../../mqtt/qmqtt_message.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QMetaType \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QHash \
		/home/<USER>/arm-qt-ssl/include/QtCore/QLoggingCategory \
		/home/<USER>/arm-qt-ssl/include/QtCore/qloggingcategory.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QLatin1String \
		/home/<USER>/arm-qt-ssl/include/QtCore/QLatin1Char \
		/home/<USER>/arm-qt-ssl/include/QtCore/QRegularExpressionMatch
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_routesubscription.o ../../mqtt/qmqtt_routesubscription.cpp

qmqtt_socket.o: ../../mqtt/qmqtt_socket.cpp ../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QTcpSocket
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_socket.o ../../mqtt/qmqtt_socket.cpp

qmqtt_ssl_socket.o: ../../mqtt/qmqtt_ssl_socket.cpp ../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtCore/QScopedPointer \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslSocket \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslError
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_ssl_socket.o ../../mqtt/qmqtt_ssl_socket.cpp

qmqtt_timer.o: ../../mqtt/qmqtt_timer.cpp ../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QtGlobal \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QTimer \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtimer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasictimer.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_timer.o ../../mqtt/qmqtt_timer.cpp

qmqtt_websocket.o: ../../mqtt/qmqtt_websocket.cpp ../../mqtt/qmqtt_websocket_p.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QObject \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QHostAddress \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qhostaddress.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qshareddata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhash.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdebug.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtextstream.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlocale.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvariant.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qset.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontiguouscache.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QString \
		/home/<USER>/arm-qt-ssl/include/QtCore/QList \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QAbstractSocket \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslConfiguration \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslerror.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcryptographichash.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qdatetime.h \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qssl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QFlags \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QNetworkRequest \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QSharedDataPointer \
		/home/<USER>/arm-qt-ssl/include/QtCore/QUrl \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qurlquery.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QVariant \
		/home/<USER>/arm-qt-ssl/include/QtNetwork/QSslError
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_websocket.o ../../mqtt/qmqtt_websocket.cpp

qmqtt_websocketiodevice.o: ../../mqtt/qmqtt_websocketiodevice.cpp ../../mqtt/qmqtt_websocketiodevice_p.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QByteArray \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qrefcount.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobal.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qconfig.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtcore-config.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsystemdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qprocessordetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcompilerdetection.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qtypeinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qsysinfo.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlogging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qflags.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qglobalstatic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmutex.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnumeric.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qversiontagging.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbasicatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qgenericatomic.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qatomic_msvc.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qnamespace.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qarraydata.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/QIODevice \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiodevice.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstring.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qchar.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringliteral.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringview.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringbuilder.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qalgorithms.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qiterator.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qhashfunctions.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpair.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvector.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qpoint.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qbytearraylist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringlist.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qregexp.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qstringmatcher.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcoreevent.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qscopedpointer.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qmetatype.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qvarlengtharray.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qcontainerfwd.h \
		/home/<USER>/arm-qt-ssl/include/QtCore/qobject_impl.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_websocketiodevice.o ../../mqtt/qmqtt_websocketiodevice.cpp

qrc_pic.o: qrc_pic.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_pic.o qrc_pic.cpp

moc_node1.o: moc_node1.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_node1.o moc_node1.cpp

moc_node2.o: moc_node2.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_node2.o moc_node2.cpp

moc_node3.o: moc_node3.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_node3.o moc_node3.cpp

moc_setting.o: moc_setting.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_setting.o moc_setting.cpp

moc_taskmqtt.o: moc_taskmqtt.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_taskmqtt.o moc_taskmqtt.cpp

moc_taskserialport.o: moc_taskserialport.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_taskserialport.o moc_taskserialport.cpp

moc_taskwarning.o: moc_taskwarning.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_taskwarning.o moc_taskwarning.cpp

moc_widget.o: moc_widget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_widget.o moc_widget.cpp

moc_qmqtt_client.o: moc_qmqtt_client.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_client.o moc_qmqtt_client.cpp

moc_qmqtt_network_p.o: moc_qmqtt_network_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_network_p.o moc_qmqtt_network_p.cpp

moc_qmqtt_networkinterface.o: moc_qmqtt_networkinterface.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_networkinterface.o moc_qmqtt_networkinterface.cpp

moc_qmqtt_router.o: moc_qmqtt_router.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_router.o moc_qmqtt_router.cpp

moc_qmqtt_routesubscription.o: moc_qmqtt_routesubscription.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_routesubscription.o moc_qmqtt_routesubscription.cpp

moc_qmqtt_socket_p.o: moc_qmqtt_socket_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_socket_p.o moc_qmqtt_socket_p.cpp

moc_qmqtt_socketinterface.o: moc_qmqtt_socketinterface.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_socketinterface.o moc_qmqtt_socketinterface.cpp

moc_qmqtt_ssl_socket_p.o: moc_qmqtt_ssl_socket_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_ssl_socket_p.o moc_qmqtt_ssl_socket_p.cpp

moc_qmqtt_timer_p.o: moc_qmqtt_timer_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_timer_p.o moc_qmqtt_timer_p.cpp

moc_qmqtt_timerinterface.o: moc_qmqtt_timerinterface.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_timerinterface.o moc_qmqtt_timerinterface.cpp

moc_qmqtt_websocket_p.o: moc_qmqtt_websocket_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_websocket_p.o moc_qmqtt_websocket_p.cpp

moc_qmqtt_websocketiodevice_p.o: moc_qmqtt_websocketiodevice_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_websocketiodevice_p.o moc_qmqtt_websocketiodevice_p.cpp

####### Install

install_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/Loong_disp/bin || mkdir -p $(INSTALL_ROOT)/opt/Loong_disp/bin
	$(QINSTALL_PROGRAM) $(QMAKE_TARGET) $(INSTALL_ROOT)/opt/Loong_disp/bin/$(QMAKE_TARGET)

uninstall_target: FORCE
	-$(DEL_FILE) $(INSTALL_ROOT)/opt/Loong_disp/bin/$(QMAKE_TARGET)
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/Loong_disp/bin/ 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

