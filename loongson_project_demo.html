<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>龙芯多传感器融合监控系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <style>
        body { 
            background: #000000; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .tech-gradient { 
            background: linear-gradient(135deg, rgba(0, 174, 239, 0.7), rgba(0, 174, 239, 0.3)); 
        }
        .card-dark { 
            background: #1a1a1a; 
            border: 1px solid #333; 
        }
        .highlight-blue { color: #00AEEF; }
        .fade-in { 
            opacity: 0; 
            transform: translateY(20px); 
            transition: all 0.6s ease; 
        }
        .fade-in.visible { 
            opacity: 1; 
            transform: translateY(0); 
        }
        .code-card {
            background: #1e1e1e;
            border: 1px solid #333;
            font-family: 'Courier New', monospace;
        }
        .performance-number {
            font-size: 4rem;
            font-weight: bold;
            color: #00AEEF;
            line-height: 1;
        }
        .tech-title {
            font-size: 2rem;
            font-weight: bold;
            color: #00AEEF;
        }
    </style>
</head>
<body class="text-white">
    <!-- 主标题区域 -->
    <div class="container mx-auto px-6 py-12">
        <div class="text-center mb-16 fade-in">
            <h1 class="text-6xl font-bold mb-4 highlight-blue">龙芯多传感器融合监控系统</h1>
            <h2 class="text-2xl text-gray-400">Loongson Multi-Sensor Fusion Monitoring System</h2>
            <p class="text-lg text-gray-300 mt-6 max-w-4xl mx-auto">
                基于龙芯架构的实时多节点传感器数据采集、处理与云端同步系统，集成MQTT通信协议与Qt图形界面
            </p>
        </div>

        <!-- 系统架构概览 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-sitemap mr-4"></i>系统架构
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-microchip text-5xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">硬件层</h3>
                    <p class="text-gray-400">龙芯处理器 + 多传感器节点</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-cogs text-5xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">应用层</h3>
                    <p class="text-gray-400">Qt框架 + 实时数据处理</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-cloud text-5xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">云端层</h3>
                    <p class="text-gray-400">MQTT协议 + 华为云IoT</p>
                </div>
            </div>
        </div>

        <!-- 核心技术点卡片 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-bolt mr-4"></i>核心技术特性
            </h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                <!-- 性能指标卡片 -->
                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">3</div>
                    <p class="text-gray-400 mt-2">传感器节点数量</p>
                    <p class="text-xs text-gray-500 mt-1">Multi-Node</p>
                </div>
                
                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">4</div>
                    <p class="text-gray-400 mt-2">监测参数类型</p>
                    <p class="text-xs text-gray-500 mt-1">Sensor Types</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">1s</div>
                    <p class="text-gray-400 mt-2">数据刷新周期</p>
                    <p class="text-xs text-gray-500 mt-1">Refresh Rate</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">5s</div>
                    <p class="text-gray-400 mt-2">节点离线检测</p>
                    <p class="text-xs text-gray-500 mt-1">Offline Detection</p>
                </div>

                <!-- 技术概念卡片 -->
                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">串口通信</div>
                    <p class="text-gray-400 mt-2">115200波特率高速数据传输</p>
                    <p class="text-xs text-gray-500 mt-1">Serial Communication</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">MQTT协议</div>
                    <p class="text-gray-400 mt-2">轻量级物联网消息传输</p>
                    <p class="text-xs text-gray-500 mt-1">IoT Protocol</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">多线程架构</div>
                    <p class="text-gray-400 mt-2">串口、MQTT、定时器独立线程</p>
                    <p class="text-xs text-gray-500 mt-1">Multi-Threading</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">实时图表</div>
                    <p class="text-gray-400 mt-2">Qt Charts动态数据可视化</p>
                    <p class="text-xs text-gray-500 mt-1">Real-time Charts</p>
                </div>
            </div>
        </div>

        <!-- 传感器监测参数 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-thermometer-half mr-4"></i>监测参数
            </h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-thermometer-half text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">温度监测</h3>
                    <p class="text-gray-400">范围: 10-50°C</p>
                    <p class="text-xs text-gray-500 mt-1">Temperature</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-tint text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">湿度监测</h3>
                    <p class="text-gray-400">范围: 10-80%RH</p>
                    <p class="text-xs text-gray-500 mt-1">Humidity</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-sun text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">光照监测</h3>
                    <p class="text-gray-400">范围: 10-1000Lux</p>
                    <p class="text-xs text-gray-500 mt-1">Light Intensity</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-smog text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">烟雾监测</h3>
                    <p class="text-gray-400">阈值: <800ppm</p>
                    <p class="text-xs text-gray-500 mt-1">Smoke Detection</p>
                </div>
            </div>
        </div>

        <!-- 代码展示卡片 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-code mr-4"></i>核心代码实现
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- MQTT数据上报核心函数 -->
                <div class="code-card rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">MQTT数据上报核心函数</h3>
                    <pre><code class="language-cpp">void taskMqtt::TaskSendToCloud() {
    // 数据格式转换
    int num_temp1 = stringTemp1.toInt();
    int num_humi1 = stringHumi1.toInt();
    int num_light1 = stringLight1.toInt();
    int num_smog1 = stringSmog1.toInt();

    // 构建JSON消息
    QString mqttMessage = "{"
        "\"services\": [{"
        "\"service_id\": \"loongson\","
        "\"properties\": {"
        "\"Node_1_Temperature\": " + QString::number(num_temp1) + ","
        "\"Node_1_Humidity\": " + QString::number(num_humi1) + ","
        "\"Node_1_Light\": " + QString::number(num_light1) + ","
        "\"Node_1_Smog\": " + QString::number(num_smog1) +
        "}}]}";

    // 发布MQTT消息
    QMQTT::Message send_msg(136, m_strPubTopic,
                           mqttMessage.toLocal8Bit(), 0);
    mqttClient->publish(send_msg);
}</code></pre>
                </div>

                <!-- 串口初始化函数 -->
                <div class="code-card rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">串口通信初始化</h3>
                    <pre><code class="language-cpp">void taskserialport::Init_SerialPort() {
    // 搜索可用串口
    QList&lt;QSerialPortInfo&gt; serialPortInfo =
        QSerialPortInfo::availablePorts();

    // 自动选择目标串口
    QString targetPort;
    foreach(const QSerialPortInfo &info, serialPortInfo) {
        if(info.description().contains("S1")) {
            targetPort = info.portName();
            break;
        }
    }

    serialPort = new QSerialPort;
    serialPort->setPortName(targetPort);

    // 配置串口参数
    serialPort->setBaudRate(QSerialPort::Baud115200);
    serialPort->setDataBits(QSerialPort::Data8);
    serialPort->setParity(QSerialPort::NoParity);
    serialPort->setStopBits(QSerialPort::OneStop);
    serialPort->setFlowControl(QSerialPort::NoFlowControl);

    // 打开串口
    serialPort->open(QIODevice::ReadWrite);
}</code></pre>
                </div>
            </div>
        </div>

        <!-- 性能监控图表 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-chart-line mr-4"></i>实时性能监控
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-4">传感器数据趋势</h3>
                    <canvas id="sensorChart" width="400" height="200"></canvas>
                </div>
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-4">节点状态分布</h3>
                    <canvas id="nodeStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- 技术规格 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-cog mr-4"></i>技术规格
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">硬件平台</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• 龙芯处理器架构</li>
                        <li>• 115200波特率串口通信</li>
                        <li>• 多传感器节点支持</li>
                        <li>• 实时数据采集</li>
                    </ul>
                </div>
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">软件框架</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Qt 5.x GUI框架</li>
                        <li>• Qt Charts数据可视化</li>
                        <li>• 多线程并发处理</li>
                        <li>• C++17标准</li>
                    </ul>
                </div>
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">通信协议</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• MQTT 3.1.1协议</li>
                        <li>• 华为云IoT平台</li>
                        <li>• JSON数据格式</li>
                        <li>• SSL/TLS安全传输</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 系统优势 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-star mr-4"></i>系统优势
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-2xl font-bold mb-4 highlight-blue">技术创新</h3>
                    <ul class="space-y-3 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>基于龙芯自主架构的嵌入式解决方案</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>多线程异步数据处理，提升系统响应性能</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>实时图表可视化，直观展示传感器数据变化</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-2xl font-bold mb-4 highlight-blue">应用价值</h3>
                    <ul class="space-y-3 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>支持环境监测、工业控制等多种应用场景</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>云端数据存储与分析，支持远程监控管理</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>模块化设计，易于扩展和维护</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化语法高亮
        hljs.highlightAll();

        // 滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // 观察所有fade-in元素
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 传感器数据趋势图表
        const sensorCtx = document.getElementById('sensorChart').getContext('2d');
        const sensorChart = new Chart(sensorCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                datasets: [{
                    label: '温度 (°C)',
                    data: [22, 20, 25, 28, 32, 29, 24],
                    borderColor: '#00AEEF',
                    backgroundColor: 'rgba(0, 174, 239, 0.1)',
                    tension: 0.4
                }, {
                    label: '湿度 (%RH)',
                    data: [45, 50, 48, 42, 38, 44, 47],
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#333333'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#333333'
                        }
                    }
                }
            }
        });

        // 节点状态分布图表
        const nodeCtx = document.getElementById('nodeStatusChart').getContext('2d');
        const nodeChart = new Chart(nodeCtx, {
            type: 'doughnut',
            data: {
                labels: ['节点1在线', '节点2在线', '节点3在线'],
                datasets: [{
                    data: [100, 100, 100],
                    backgroundColor: [
                        '#00AEEF',
                        '#4CAF50',
                        '#FF9800'
                    ],
                    borderWidth: 2,
                    borderColor: '#1a1a1a'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#ffffff',
                            padding: 20
                        }
                    }
                }
            }
        });

        // 模拟实时数据更新
        setInterval(() => {
            // 更新传感器数据
            sensorChart.data.datasets[0].data = sensorChart.data.datasets[0].data.map(() =>
                Math.floor(Math.random() * 20) + 20
            );
            sensorChart.data.datasets[1].data = sensorChart.data.datasets[1].data.map(() =>
                Math.floor(Math.random() * 30) + 35
            );
            sensorChart.update('none');
        }, 3000);
    </script>
</body>
</html>
