<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>龙芯多传感器融合监控系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <style>
        body { 
            background: #000000; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .tech-gradient { 
            background: linear-gradient(135deg, rgba(0, 174, 239, 0.7), rgba(0, 174, 239, 0.3)); 
        }
        .card-dark { 
            background: #1a1a1a; 
            border: 1px solid #333; 
        }
        .highlight-blue { color: #00AEEF; }
        .fade-in { 
            opacity: 0; 
            transform: translateY(20px); 
            transition: all 0.6s ease; 
        }
        .fade-in.visible { 
            opacity: 1; 
            transform: translateY(0); 
        }
        .code-card {
            background: #1e1e1e;
            border: 1px solid #333;
            font-family: 'Courier New', monospace;
        }
        .performance-number {
            font-size: 4rem;
            font-weight: bold;
            color: #00AEEF;
            line-height: 1;
        }
        .tech-title {
            font-size: 2rem;
            font-weight: bold;
            color: #00AEEF;
        }
    </style>
</head>
<body class="text-white">
    <!-- 主标题区域 -->
    <div class="container mx-auto px-6 py-12">
        <div class="text-center mb-16 fade-in">
            <h1 class="text-6xl font-bold mb-4 highlight-blue">龙芯多传感器融合监控系统</h1>
            <h2 class="text-2xl text-gray-400">Loongson Multi-Sensor Fusion Monitoring System</h2>
            <p class="text-lg text-gray-300 mt-6 max-w-4xl mx-auto">
                基于龙芯架构的实时多节点传感器数据采集、处理与云端同步系统，集成MQTT通信协议与Qt图形界面
            </p>
        </div>

        <!-- 系统架构概览 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-sitemap mr-4"></i>系统架构
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-microchip text-5xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">硬件层</h3>
                    <p class="text-gray-400">龙芯处理器 + 多传感器节点</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-cogs text-5xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">应用层</h3>
                    <p class="text-gray-400">Qt框架 + 实时数据处理</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-cloud text-5xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">云端层</h3>
                    <p class="text-gray-400">华为云IoT + Web可视化</p>
                </div>
            </div>
        </div>

        <!-- 核心技术点卡片 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-bolt mr-4"></i>核心技术特性
            </h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                <!-- 性能指标卡片 -->
                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">3</div>
                    <p class="text-gray-400 mt-2">传感器节点数量</p>
                    <p class="text-xs text-gray-500 mt-1">Multi-Node</p>
                </div>
                
                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">4</div>
                    <p class="text-gray-400 mt-2">监测参数类型</p>
                    <p class="text-xs text-gray-500 mt-1">Sensor Types</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">1s</div>
                    <p class="text-gray-400 mt-2">数据刷新周期</p>
                    <p class="text-xs text-gray-500 mt-1">Refresh Rate</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">5s</div>
                    <p class="text-gray-400 mt-2">节点离线检测</p>
                    <p class="text-xs text-gray-500 mt-1">Offline Detection</p>
                </div>

                <!-- 技术概念卡片 -->
                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">串口通信</div>
                    <p class="text-gray-400 mt-2">115200波特率高速数据传输</p>
                    <p class="text-xs text-gray-500 mt-1">Serial Communication</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">MQTT协议</div>
                    <p class="text-gray-400 mt-2">轻量级物联网消息传输</p>
                    <p class="text-xs text-gray-500 mt-1">IoT Protocol</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">多线程架构</div>
                    <p class="text-gray-400 mt-2">串口、MQTT、定时器独立线程</p>
                    <p class="text-xs text-gray-500 mt-1">Multi-Threading</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">实时图表</div>
                    <p class="text-gray-400 mt-2">Qt Charts动态数据可视化</p>
                    <p class="text-xs text-gray-500 mt-1">Real-time Charts</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">AI数据分析</div>
                    <p class="text-gray-400 mt-2">DeepSeek大模型智能分析</p>
                    <p class="text-xs text-gray-500 mt-1">AI Analytics</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">95%</div>
                    <p class="text-gray-400 mt-2">AI预测准确率</p>
                    <p class="text-xs text-gray-500 mt-1">AI Accuracy</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">50ms</div>
                    <p class="text-gray-400 mt-2">AI推理响应时间</p>
                    <p class="text-xs text-gray-500 mt-1">AI Response</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">华为云集成</div>
                    <p class="text-gray-400 mt-2">IoT设备管理与数据存储</p>
                    <p class="text-xs text-gray-500 mt-1">Huawei Cloud</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="tech-title">Web可视化</div>
                    <p class="text-gray-400 mt-2">云端API数据Web展示</p>
                    <p class="text-xs text-gray-500 mt-1">Web Dashboard</p>
                </div>

                <div class="card-dark rounded-lg p-6 text-center">
                    <div class="performance-number">99.9%</div>
                    <p class="text-gray-400 mt-2">云端服务可用性</p>
                    <p class="text-xs text-gray-500 mt-1">Cloud Uptime</p>
                </div>
            </div>
        </div>

        <!-- 华为云集成模块 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-cloud mr-4"></i>华为云IoT平台集成
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-2xl font-bold mb-6 highlight-blue">云端数据流程</h3>
                    <div class="space-y-4">
                        <div class="flex items-center p-4 card-dark rounded-lg">
                            <i class="fas fa-upload text-2xl highlight-blue mr-4"></i>
                            <div>
                                <h4 class="font-bold text-lg">设备数据上传</h4>
                                <p class="text-gray-400">通过MQTT协议实时上传传感器数据到华为云</p>
                            </div>
                        </div>
                        <div class="flex items-center p-4 card-dark rounded-lg">
                            <i class="fas fa-database text-2xl highlight-blue mr-4"></i>
                            <div>
                                <h4 class="font-bold text-lg">云端数据存储</h4>
                                <p class="text-gray-400">华为云IoT平台安全存储和管理设备数据</p>
                            </div>
                        </div>
                        <div class="flex items-center p-4 card-dark rounded-lg">
                            <i class="fas fa-api text-2xl highlight-blue mr-4"></i>
                            <div>
                                <h4 class="font-bold text-lg">API接口调用</h4>
                                <p class="text-gray-400">通过RESTful API获取云端存储的历史数据</p>
                            </div>
                        </div>
                        <div class="flex items-center p-4 card-dark rounded-lg">
                            <i class="fas fa-globe text-2xl highlight-blue mr-4"></i>
                            <div>
                                <h4 class="font-bold text-lg">Web可视化部署</h4>
                                <p class="text-gray-400">将设备数据可视化部署到Web页面展示</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-2xl font-bold mb-6 highlight-blue">云端服务状态</h3>
                    <div class="space-y-4">
                        <div class="p-4 tech-gradient rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-bold">华为云连接状态</span>
                                <span class="text-2xl font-bold text-green-400">在线</span>
                            </div>
                            <p class="text-sm text-gray-200">设备ID: 685a734ad582f2001834985f_loong_1</p>
                        </div>
                        <div class="p-4 card-dark rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-bold">数据上传频率</span>
                                <span class="text-lg font-bold highlight-blue">每30秒</span>
                            </div>
                            <p class="text-sm text-gray-300">最近上传: 2分钟前</p>
                        </div>
                        <div class="p-4 card-dark rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-bold">API调用统计</span>
                                <span class="text-lg font-bold highlight-blue">1,247次/天</span>
                            </div>
                            <p class="text-sm text-gray-300">平均响应时间: 120ms</p>
                        </div>
                        <div class="p-4 card-dark rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-bold">Web访问量</span>
                                <span class="text-lg font-bold highlight-blue">856次/天</span>
                            </div>
                            <p class="text-sm text-gray-300">实时在线用户: 12人</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- DeepSeek AI 智能分析模块 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-brain mr-4"></i>DeepSeek AI 智能数据分析
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-2xl font-bold mb-6 highlight-blue">AI分析能力</h3>
                    <div class="space-y-4">
                        <div class="flex items-center p-4 card-dark rounded-lg">
                            <i class="fas fa-chart-line text-2xl highlight-blue mr-4"></i>
                            <div>
                                <h4 class="font-bold text-lg">趋势预测分析</h4>
                                <p class="text-gray-400">基于历史数据预测传感器数值变化趋势</p>
                            </div>
                        </div>
                        <div class="flex items-center p-4 card-dark rounded-lg">
                            <i class="fas fa-exclamation-triangle text-2xl highlight-blue mr-4"></i>
                            <div>
                                <h4 class="font-bold text-lg">异常检测识别</h4>
                                <p class="text-gray-400">智能识别传感器数据异常模式和潜在故障</p>
                            </div>
                        </div>
                        <div class="flex items-center p-4 card-dark rounded-lg">
                            <i class="fas fa-lightbulb text-2xl highlight-blue mr-4"></i>
                            <div>
                                <h4 class="font-bold text-lg">智能建议生成</h4>
                                <p class="text-gray-400">根据环境数据提供优化建议和维护提醒</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-2xl font-bold mb-6 highlight-blue">实时AI分析结果</h3>
                    <div class="space-y-4">
                        <div class="p-4 tech-gradient rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-bold">环境舒适度评估</span>
                                <span class="text-2xl font-bold">85分</span>
                            </div>
                            <p class="text-sm text-gray-200">当前温湿度配置良好，建议保持现状</p>
                        </div>
                        <div class="p-4 card-dark rounded-lg border border-yellow-500">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-bold text-yellow-400">预警提醒</span>
                                <i class="fas fa-warning text-yellow-400"></i>
                            </div>
                            <p class="text-sm text-gray-300">节点2光照传感器数值波动较大，建议检查</p>
                        </div>
                        <div class="p-4 card-dark rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-bold">下一小时预测</span>
                                <span class="text-sm text-gray-400">置信度: 94%</span>
                            </div>
                            <p class="text-sm text-gray-300">温度预计上升2-3°C，湿度保持稳定</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 传感器监测参数 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-thermometer-half mr-4"></i>监测参数
            </h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-thermometer-half text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">温度监测</h3>
                    <p class="text-gray-400">范围: 10-50°C</p>
                    <p class="text-xs text-gray-500 mt-1">Temperature</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-tint text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">湿度监测</h3>
                    <p class="text-gray-400">范围: 10-80%RH</p>
                    <p class="text-xs text-gray-500 mt-1">Humidity</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-sun text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">光照监测</h3>
                    <p class="text-gray-400">范围: 10-1000Lux</p>
                    <p class="text-xs text-gray-500 mt-1">Light Intensity</p>
                </div>
                <div class="card-dark rounded-lg p-6 text-center">
                    <i class="fas fa-smog text-4xl highlight-blue mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">烟雾监测</h3>
                    <p class="text-gray-400">阈值: <800ppm</p>
                    <p class="text-xs text-gray-500 mt-1">Smoke Detection</p>
                </div>
            </div>
        </div>

        <!-- 代码展示卡片 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-code mr-4"></i>核心代码实现
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 华为云API调用接口 -->
                <div class="code-card rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">华为云API数据获取接口</h3>
                    <pre><code class="language-cpp">class HuaweiCloudAPI {
private:
    QString accessToken;
    QString baseUrl = "https://iotda.cn-north-4.myhuaweicloud.com";
    QString deviceId = "685a734ad582f2001834985f_loong_1";

public:
    QJsonObject getDeviceData(const QString& startTime,
                             const QString& endTime) {
        QString url = QString("%1/v5/iot/%2/devices/%3/properties")
                     .arg(baseUrl)
                     .arg(projectId)
                     .arg(deviceId);

        QNetworkRequest request;
        request.setUrl(QUrl(url));
        request.setRawHeader("X-Auth-Token", accessToken.toUtf8());
        request.setRawHeader("Content-Type", "application/json");

        QJsonObject params;
        params["start_time"] = startTime;
        params["end_time"] = endTime;
        params["limit"] = 1000;

        // 发送GET请求获取历史数据
        QNetworkReply* reply = manager->get(request);
        return parseResponse(reply);
    }

    void deployToWeb(const QJsonObject& data) {
        // 将数据部署到Web可视化页面
        generateWebDashboard(data);
    }
};</code></pre>
                </div>

                <!-- DeepSeek AI分析接口 -->
                <div class="code-card rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">DeepSeek AI数据分析接口</h3>
                    <pre><code class="language-cpp">class DeepSeekAnalyzer {
private:
    QString apiKey = "your_deepseek_api_key";
    QString apiUrl = "https://api.deepseek.com/v1/chat/completions";

public:
    QString analyzeSensorData(const SensorData& data) {
        QJsonObject request;
        request["model"] = "deepseek-chat";

        // 构建分析提示
        QString prompt = QString(
            "分析传感器数据: 温度%1°C, 湿度%2%, "
            "光照%3Lux, 烟雾%4ppm. "
            "请提供环境评估和建议。")
            .arg(data.temperature)
            .arg(data.humidity)
            .arg(data.light)
            .arg(data.smoke);

        QJsonArray messages;
        QJsonObject message;
        message["role"] = "user";
        message["content"] = prompt;
        messages.append(message);
        request["messages"] = messages;

        // 发送API请求
        return sendApiRequest(request);
    }
};</code></pre>
                </div>

                <!-- MQTT数据上报核心函数 -->
                <div class="code-card rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">MQTT数据上报核心函数</h3>
                    <pre><code class="language-cpp">void taskMqtt::TaskSendToCloud() {
    // 数据格式转换
    int num_temp1 = stringTemp1.toInt();
    int num_humi1 = stringHumi1.toInt();
    int num_light1 = stringLight1.toInt();
    int num_smog1 = stringSmog1.toInt();

    // 构建JSON消息
    QString mqttMessage = "{"
        "\"services\": [{"
        "\"service_id\": \"loongson\","
        "\"properties\": {"
        "\"Node_1_Temperature\": " + QString::number(num_temp1) + ","
        "\"Node_1_Humidity\": " + QString::number(num_humi1) + ","
        "\"Node_1_Light\": " + QString::number(num_light1) + ","
        "\"Node_1_Smog\": " + QString::number(num_smog1) +
        "}}]}";

    // 发布MQTT消息
    QMQTT::Message send_msg(136, m_strPubTopic,
                           mqttMessage.toLocal8Bit(), 0);
    mqttClient->publish(send_msg);
}</code></pre>
                </div>

                <!-- 串口初始化函数 -->
                <div class="code-card rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">串口通信初始化</h3>
                    <pre><code class="language-cpp">void taskserialport::Init_SerialPort() {
    // 搜索可用串口
    QList&lt;QSerialPortInfo&gt; serialPortInfo =
        QSerialPortInfo::availablePorts();

    // 自动选择目标串口
    QString targetPort;
    foreach(const QSerialPortInfo &info, serialPortInfo) {
        if(info.description().contains("S1")) {
            targetPort = info.portName();
            break;
        }
    }

    serialPort = new QSerialPort;
    serialPort->setPortName(targetPort);

    // 配置串口参数
    serialPort->setBaudRate(QSerialPort::Baud115200);
    serialPort->setDataBits(QSerialPort::Data8);
    serialPort->setParity(QSerialPort::NoParity);
    serialPort->setStopBits(QSerialPort::OneStop);
    serialPort->setFlowControl(QSerialPort::NoFlowControl);

    // 打开串口
    serialPort->open(QIODevice::ReadWrite);
}</code></pre>
                </div>
            </div>
        </div>

        <!-- 性能监控图表 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-chart-line mr-4"></i>实时性能监控
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-4">传感器数据趋势</h3>
                    <canvas id="sensorChart" width="400" height="200"></canvas>
                </div>
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-4">节点状态分布</h3>
                    <canvas id="nodeStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- 技术规格 -->
        <div class="mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-cog mr-4"></i>技术规格
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">硬件平台</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• 龙芯处理器架构</li>
                        <li>• 115200波特率串口通信</li>
                        <li>• 多传感器节点支持</li>
                        <li>• 实时数据采集</li>
                    </ul>
                </div>
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">软件框架</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Qt 5.x GUI框架</li>
                        <li>• Qt Charts数据可视化</li>
                        <li>• 多线程并发处理</li>
                        <li>• C++17标准</li>
                    </ul>
                </div>
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">华为云集成</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• 华为云IoT设备管理平台</li>
                        <li>• RESTful API数据接口</li>
                        <li>• 云端数据存储与管理</li>
                        <li>• Web可视化部署</li>
                    </ul>
                </div>
                <div class="card-dark rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 highlight-blue">AI智能分析</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• DeepSeek大语言模型</li>
                        <li>• RESTful API集成</li>
                        <li>• 实时数据分析</li>
                        <li>• 智能预测算法</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 系统优势 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <h2 class="text-4xl font-bold mb-8 highlight-blue flex items-center">
                <i class="fas fa-star mr-4"></i>系统优势
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-2xl font-bold mb-4 highlight-blue">技术创新</h3>
                    <ul class="space-y-3 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>基于龙芯自主架构的嵌入式解决方案</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>多线程异步数据处理，提升系统响应性能</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>实时图表可视化，直观展示传感器数据变化</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>集成DeepSeek大模型，提供智能数据分析和预测</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>华为云IoT平台集成，确保数据安全可靠存储</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-2xl font-bold mb-4 highlight-blue">应用价值</h3>
                    <ul class="space-y-3 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>支持环境监测、工业控制等多种应用场景</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>云端数据存储与分析，支持远程监控管理</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>模块化设计，易于扩展和维护</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>AI驱动的智能决策，提升系统自动化水平</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle highlight-blue mr-3 mt-1"></i>
                            <span>Web可视化部署，支持跨平台远程访问和监控</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化语法高亮
        hljs.highlightAll();

        // 滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // 观察所有fade-in元素
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 传感器数据趋势图表
        const sensorCtx = document.getElementById('sensorChart').getContext('2d');
        const sensorChart = new Chart(sensorCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                datasets: [{
                    label: '温度 (°C)',
                    data: [22, 20, 25, 28, 32, 29, 24],
                    borderColor: '#00AEEF',
                    backgroundColor: 'rgba(0, 174, 239, 0.1)',
                    tension: 0.4
                }, {
                    label: '湿度 (%RH)',
                    data: [45, 50, 48, 42, 38, 44, 47],
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#333333'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#333333'
                        }
                    }
                }
            }
        });

        // 节点状态分布图表
        const nodeCtx = document.getElementById('nodeStatusChart').getContext('2d');
        const nodeChart = new Chart(nodeCtx, {
            type: 'doughnut',
            data: {
                labels: ['节点1在线', '节点2在线', '节点3在线'],
                datasets: [{
                    data: [100, 100, 100],
                    backgroundColor: [
                        '#00AEEF',
                        '#4CAF50',
                        '#FF9800'
                    ],
                    borderWidth: 2,
                    borderColor: '#1a1a1a'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#ffffff',
                            padding: 20
                        }
                    }
                }
            }
        });

        // 模拟实时数据更新
        setInterval(() => {
            // 更新传感器数据
            sensorChart.data.datasets[0].data = sensorChart.data.datasets[0].data.map(() =>
                Math.floor(Math.random() * 20) + 20
            );
            sensorChart.data.datasets[1].data = sensorChart.data.datasets[1].data.map(() =>
                Math.floor(Math.random() * 30) + 35
            );
            sensorChart.update('none');
        }, 3000);

        // 模拟AI分析结果更新
        const aiAnalysisTexts = [
            "当前温湿度配置良好，建议保持现状",
            "环境参数稳定，系统运行正常",
            "检测到轻微温度波动，属于正常范围",
            "湿度水平适宜，有利于设备稳定运行",
            "光照强度变化符合预期模式"
        ];

        const aiWarnings = [
            "节点2光照传感器数值波动较大，建议检查",
            "节点1温度传感器读数异常，请关注",
            "节点3烟雾传感器灵敏度需要校准",
            "系统整体运行良好，无异常警告",
            "建议定期清洁传感器以保持精度"
        ];

        const aiPredictions = [
            "温度预计上升2-3°C，湿度保持稳定",
            "未来1小时内环境参数将保持稳定",
            "预测光照强度将在30分钟后下降",
            "温湿度变化趋势平缓，无需干预",
            "系统负载预计在正常范围内波动"
        ];

        setInterval(() => {
            // 随机更新AI分析文本
            const analysisElement = document.querySelector('.tech-gradient p');
            const warningElement = document.querySelector('.border-yellow-500 p');
            const predictionElement = document.querySelector('.card-dark:last-child p');

            if (analysisElement) {
                analysisElement.textContent = aiAnalysisTexts[Math.floor(Math.random() * aiAnalysisTexts.length)];
            }
            if (warningElement) {
                warningElement.textContent = aiWarnings[Math.floor(Math.random() * aiWarnings.length)];
            }
            if (predictionElement) {
                predictionElement.textContent = aiPredictions[Math.floor(Math.random() * aiPredictions.length)];
            }

            // 更新置信度
            const confidenceElement = document.querySelector('.text-gray-400');
            if (confidenceElement && confidenceElement.textContent.includes('置信度')) {
                const confidence = Math.floor(Math.random() * 10) + 90;
                confidenceElement.textContent = `置信度: ${confidence}%`;
            }
        }, 8000);

        // 模拟华为云服务状态更新
        const cloudStatuses = ['在线', '同步中', '在线'];
        const uploadTimes = ['刚刚', '1分钟前', '2分钟前', '3分钟前'];
        const apiCalls = [1247, 1248, 1249, 1250, 1251];
        const webVisits = [856, 857, 858, 859, 860];
        const onlineUsers = [12, 13, 11, 14, 10];

        setInterval(() => {
            // 更新华为云连接状态
            const statusElement = document.querySelector('.text-green-400');
            if (statusElement) {
                statusElement.textContent = cloudStatuses[Math.floor(Math.random() * cloudStatuses.length)];
            }

            // 更新最近上传时间
            const uploadElement = document.querySelector('.text-sm.text-gray-300');
            if (uploadElement && uploadElement.textContent.includes('最近上传')) {
                uploadElement.textContent = `最近上传: ${uploadTimes[Math.floor(Math.random() * uploadTimes.length)]}`;
            }

            // 更新API调用统计
            const apiElements = document.querySelectorAll('.text-lg.font-bold.highlight-blue');
            if (apiElements.length >= 3) {
                apiElements[1].textContent = `${apiCalls[Math.floor(Math.random() * apiCalls.length)]}次/天`;
                apiElements[2].textContent = `${webVisits[Math.floor(Math.random() * webVisits.length)]}次/天`;
            }

            // 更新在线用户数
            const onlineElement = document.querySelector('.text-sm.text-gray-300:last-child');
            if (onlineElement && onlineElement.textContent.includes('实时在线用户')) {
                onlineElement.textContent = `实时在线用户: ${onlineUsers[Math.floor(Math.random() * onlineUsers.length)]}人`;
            }
        }, 5000);
    </script>
</body>
</html>
