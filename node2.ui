<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>node2</class>
 <widget class="QWidget" name="node2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1197</width>
    <height>698</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>节点二</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(246, 245, 244);</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0" rowspan="2">
    <widget class="QPushButton" name="back_2Button">
     <property name="minimumSize">
      <size>
       <width>70</width>
       <height>70</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">border-image: url(:/back.png);</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="1" colspan="2">
    <spacer name="horizontalSpacer_4">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>364</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="3" rowspan="2">
    <layout class="QVBoxLayout" name="verticalLayout_7">
     <item>
      <widget class="QLabel" name="label_time">
       <property name="minimumSize">
        <size>
         <width>250</width>
         <height>0</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(28, 113, 216);</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="textFormat">
        <enum>Qt::AutoText</enum>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_data">
       <property name="text">
        <string>数据导出</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="4" rowspan="3">
    <layout class="QVBoxLayout" name="verticalLayout_6">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QPushButton" name="temperature2Button">
         <property name="minimumSize">
          <size>
           <width>85</width>
           <height>62</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(246, 97, 81);</string>
         </property>
         <property name="text">
          <string>温度曲线</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>18</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="humi_2Button">
         <property name="minimumSize">
          <size>
           <width>85</width>
           <height>62</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(153, 193, 241);</string>
         </property>
         <property name="text">
          <string>湿度曲线</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="light_2Button">
         <property name="minimumSize">
          <size>
           <width>85</width>
           <height>62</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(249, 240, 107);</string>
         </property>
         <property name="text">
          <string>光照曲线</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="smog_2Button">
         <property name="minimumSize">
          <size>
           <width>85</width>
           <height>62</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(119, 118, 123);</string>
         </property>
         <property name="text">
          <string>烟雾曲线</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QChartView" name="graphicsView">
       <property name="minimumSize">
        <size>
         <width>450</width>
         <height>450</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0" rowspan="2" colspan="2">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="text">
        <string>节点日志</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QTextEdit" name="textEdit_log">
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="2" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="label_2">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>41</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">image: url(:/temp1_img.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="textEdit_temp">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QLabel" name="label_3">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>41</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">image: url(:/humi1_image.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="textEdit_humi">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="3" column="2" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <widget class="QLabel" name="label_4">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>41</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">image: url(:/light1_img.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="textEdit_light">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item>
        <widget class="QLabel" name="label_5">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>41</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">image: url(:/smog1_img.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="textEdit_smog">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QChartView</class>
   <extends>QGraphicsView</extends>
   <header location="global">qchartview.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
