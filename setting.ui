<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>setting</class>
 <widget class="QWidget" name="setting">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>925</width>
    <height>627</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>设置</string>
  </property>
  <widget class="QPushButton" name="backButton">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>60</y>
     <width>61</width>
     <height>71</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>70</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">border-image: url(:/back.png);</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="label_time">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>40</y>
     <width>231</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>196</x>
     <y>141</y>
     <width>471</width>
     <height>321</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout_5">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_10">
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label">
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>16</pointsize>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>报警值设置</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
    <item>
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_8">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="text">
               <string>温度上限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="doubleSpinBox_tempMax">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="value">
               <double>50.000000000000000</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QLabel" name="label_3">
              <property name="text">
               <string>温度下限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="doubleSpinBox_tempMin">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="value">
               <double>10.000000000000000</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QLabel" name="label_4">
              <property name="text">
               <string>湿度上限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="doubleSpinBox_humiMax">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="value">
               <double>80.000000000000000</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QLabel" name="label_5">
              <property name="text">
               <string>湿度下限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="doubleSpinBox_humiMin">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="value">
               <double>10.000000000000000</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_9">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <item>
             <widget class="QLabel" name="label_6">
              <property name="text">
               <string>光照上限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="doubleSpinBox_lightMax">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximum">
               <double>1000.000000000000000</double>
              </property>
              <property name="value">
               <double>1000.000000000000000</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <item>
             <widget class="QLabel" name="label_7">
              <property name="text">
               <string>光照下限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="doubleSpinBox_lightMin">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximum">
               <double>1000.000000000000000</double>
              </property>
              <property name="value">
               <double>10.000000000000000</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_7">
          <item>
           <widget class="QLabel" name="label_8">
            <property name="text">
             <string>烟雾报警值</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="doubleSpinBox_smogMax">
            <property name="minimumSize">
             <size>
              <width>90</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximum">
             <double>1000.000000000000000</double>
            </property>
            <property name="value">
             <double>800.000000000000000</double>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
