<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Widget</class>
 <widget class="QWidget" name="Widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1437</width>
    <height>896</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>主界面</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="1" colspan="3">
    <widget class="QLabel" name="label_7">
     <property name="font">
      <font>
       <family>Ubuntu Sans</family>
       <pointsize>25</pointsize>
       <italic>false</italic>
       <bold>false</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string>龙芯环境检测系统</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="5">
    <spacer name="verticalSpacer_7">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>75</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="1">
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QLabel" name="label_5">
       <property name="maximumSize">
        <size>
         <width>60</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>14</pointsize>
        </font>
       </property>
       <property name="text">
        <string>时间：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_time">
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>0</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(53, 132, 228);</string>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="5" rowspan="4">
    <layout class="QVBoxLayout" name="verticalLayout_5">
     <item>
      <widget class="QLabel" name="label_6">
       <property name="font">
        <font>
         <pointsize>17</pointsize>
        </font>
       </property>
       <property name="text">
        <string>AI对话框</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QTextEdit" name="outPutEdit">
       <property name="minimumSize">
        <size>
         <width>400</width>
         <height>450</height>
        </size>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_8">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>18</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QTextEdit" name="inputEdit">
       <property name="minimumSize">
        <size>
         <width>400</width>
         <height>150</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>151</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <spacer name="horizontalSpacer_6">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string>发送</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="2" column="2">
    <spacer name="verticalSpacer_4">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>232</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="3">
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>232</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="0">
    <spacer name="horizontalSpacer_4">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>95</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="1" colspan="3">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QPushButton" name="node1Bt">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>150</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border-image: url(:/node_pic.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="font">
          <font>
           <pointsize>20</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Node1</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>18</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QPushButton" name="node2Bt">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>150</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border-image: url(:/node_pic.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_2">
         <property name="font">
          <font>
           <pointsize>20</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Node2</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>18</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QPushButton" name="node3Bt">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>150</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border-image: url(:/node_pic.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_3">
         <property name="font">
          <font>
           <pointsize>20</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Node3</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>17</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <widget class="QPushButton" name="setBt">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>150</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border-image: url(:/set.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_4">
         <property name="font">
          <font>
           <pointsize>20</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Set</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="3" column="4">
    <spacer name="horizontalSpacer_7">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>95</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="6">
    <spacer name="horizontalSpacer_5">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>95</width>
       <height>18</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="4" column="2">
    <spacer name="verticalSpacer_6">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>232</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="4" column="3">
    <spacer name="verticalSpacer_3">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>232</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="5" column="5">
    <spacer name="verticalSpacer_5">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>18</width>
       <height>74</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
